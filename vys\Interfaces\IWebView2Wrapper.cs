using System.Threading.Tasks;

namespace vys.Interfaces
{
    /// <summary>
    /// Interface for WebView2 operations to enable testing
    /// </summary>
    public interface IWebView2Wrapper
    {
        /// <summary>
        /// Executes JavaScript in the WebView2
        /// </summary>
        /// <param name="javaScript">JavaScript code to execute</param>
        /// <returns>Result of the script execution</returns>
        Task<string> ExecuteScriptAsync(string javaScript);

        /// <summary>
        /// Navigates to the specified URI
        /// </summary>
        /// <param name="uri">URI to navigate to</param>
        void Navigate(string uri);

        /// <summary>
        /// Reloads the current page
        /// </summary>
        Task ReloadAsync();

        /// <summary>
        /// Gets the current source URI
        /// </summary>
        string Source { get; }
    }
}
