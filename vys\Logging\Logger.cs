using System;
using System.IO;
using System.Runtime.CompilerServices;

namespace vys.Logging
{
    /// <summary>
    /// Configurable logging system for VYS Browser
    /// Supports different log levels and debug/production modes
    /// </summary>
    public static class Logger
    {
        private static LogLevel _currentLogLevel = LogLevel.Warning; // Production default
        private static bool _isDebugMode = false;
        private static readonly object _lockObject = new object();
        private static string? _logFilePath;

        /// <summary>
        /// Available log levels
        /// </summary>
        public enum LogLevel
        {
            Debug = 0,
            Info = 1,
            Warning = 2,
            Error = 3
        }

        /// <summary>
        /// Initialize the logger with configuration
        /// </summary>
        /// <param name="isDebugMode">Enable debug mode for verbose logging</param>
        /// <param name="logFilePath">Optional file path for logging (null for console only)</param>
        public static void Initialize(bool isDebugMode = false, string? logFilePath = null)
        {
            _isDebugMode = isDebugMode;
            _logFilePath = logFilePath;
            
            // Set log level based on mode - reduced verbosity
            _currentLogLevel = isDebugMode ? LogLevel.Info : LogLevel.Warning;
            
            // Create log file directory if specified
            if (!string.IsNullOrEmpty(_logFilePath))
            {
                var directory = Path.GetDirectoryName(_logFilePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
            
            // Log initialization
            Info("Logger", $"Logger initialized - Debug Mode: {isDebugMode}, Log Level: {_currentLogLevel}");
            if (!string.IsNullOrEmpty(_logFilePath))
            {
                Info("Logger", $"Log file: {_logFilePath}");
            }
        }

        /// <summary>
        /// Log a debug message (only shown in debug mode)
        /// </summary>
        public static void Debug(string category, string message, [CallerMemberName] string memberName = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Debug, category, message, memberName, lineNumber);
        }

        /// <summary>
        /// Log an informational message
        /// </summary>
        public static void Info(string category, string message, [CallerMemberName] string memberName = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Info, category, message, memberName, lineNumber);
        }

        /// <summary>
        /// Log a warning message
        /// </summary>
        public static void Warning(string category, string message, [CallerMemberName] string memberName = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Warning, category, message, memberName, lineNumber);
        }

        /// <summary>
        /// Log an error message
        /// </summary>
        public static void Error(string category, string message, [CallerMemberName] string memberName = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Error, category, message, memberName, lineNumber);
        }

        /// <summary>
        /// Log an exception
        /// </summary>
        public static void Error(string category, Exception exception, string? additionalMessage = null, [CallerMemberName] string memberName = "", [CallerLineNumber] int lineNumber = 0)
        {
            var message = additionalMessage != null 
                ? $"{additionalMessage}: {exception.Message}"
                : exception.Message;
            
            Log(LogLevel.Error, category, message, memberName, lineNumber);
            
            // In debug mode, also log stack trace
            if (_isDebugMode && !string.IsNullOrEmpty(exception.StackTrace))
            {
                Log(LogLevel.Debug, category, $"Stack trace: {exception.StackTrace}", memberName, lineNumber);
            }
        }

        /// <summary>
        /// Core logging method
        /// </summary>
        private static void Log(LogLevel level, string category, string message, string memberName, int lineNumber)
        {
            // Check if this log level should be shown
            if (level < _currentLogLevel)
                return;

            lock (_lockObject)
            {
                try
                {
                    var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                    var levelIcon = GetLevelIcon(level);
                    var levelName = level.ToString().ToUpper();
                    
                    string logMessage;
                    if (_isDebugMode)
                    {
                        // Detailed format for debug mode
                        logMessage = $"[{timestamp}] {levelIcon} {levelName} [{category}] {message} ({memberName}:{lineNumber})";
                    }
                    else
                    {
                        // Simplified format for production
                        logMessage = $"{levelIcon} [{category}] {message}";
                    }

                    // Output to console
                    if (level >= LogLevel.Error)
                    {
                        Console.Error.WriteLine(logMessage);
                    }
                    else
                    {
                        Console.WriteLine(logMessage);
                    }

                    // Output to file if configured
                    if (!string.IsNullOrEmpty(_logFilePath))
                    {
                        File.AppendAllText(_logFilePath, logMessage + Environment.NewLine);
                    }
                }
                catch (Exception ex)
                {
                    // Fallback logging to prevent logging system from crashing the app
                    Console.Error.WriteLine($"Logger error: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Get icon/emoji for log level
        /// </summary>
        private static string GetLevelIcon(LogLevel level)
        {
            return level switch
            {
                LogLevel.Debug => "🔍",
                LogLevel.Info => "ℹ️",
                LogLevel.Warning => "⚠️",
                LogLevel.Error => "❌",
                _ => "📝"
            };
        }

        /// <summary>
        /// Check if a log level is enabled
        /// </summary>
        public static bool IsEnabled(LogLevel level)
        {
            return level >= _currentLogLevel;
        }

        /// <summary>
        /// Get current debug mode status
        /// </summary>
        public static bool IsDebugMode => _isDebugMode;

        /// <summary>
        /// Get current log level
        /// </summary>
        public static LogLevel CurrentLogLevel => _currentLogLevel;
    }
}
