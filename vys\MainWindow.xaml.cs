﻿using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.Wpf;
using System.Drawing;
using System.ComponentModel;
using System.Windows.Controls;
using System.Text.Json;
using System.Windows.Media;
using vys.Utils.Audio;
using vys.Logging;

namespace vys;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private StatusManager? _statusManager;
    private SpotifyLoginDetector? _loginDetector;
    private AudioManager? _audioManager;
    private SpotifyAutomation? _spotifyAutomation;
    public bool isDevToolsVisible = false;

    // Rate limiting for mute button
    private DateTime _lastMuteToggleTime = DateTime.MinValue;
    private readonly TimeSpan _muteToggleCooldown = TimeSpan.FromMilliseconds(1500); // 1.5 second cooldown
    private bool _isMuteOperationInProgress = false;
    public MainWindow()
    {
        Logger.Debug("MainWindow", "Constructor starting...");
        try
        {
            InitializeComponent();
            Logger.Debug("MainWindow", "InitializeComponent completed");

            // Initialize status management
            _statusManager = new StatusManager();
            Logger.Debug("MainWindow", "StatusManager initialized");

            // Initialize audio management
            _audioManager = new AudioManager();
            _audioManager.MuteStateChanged += OnMuteStateChanged;
            Logger.Info("MainWindow", "AudioManager initialized");

            InitializeBrowser();
            Logger.Debug("MainWindow", "InitializeBrowser completed");
            SetupKeyboardShortcuts();
            Logger.Debug("MainWindow", "SetupKeyboardShortcuts completed");

            // Ensure UI is updated after everything is initialized
            Dispatcher.BeginInvoke(() =>
            {
                InitializeUIState();
            });

            Logger.Info("MainWindow", "MainWindow constructor completed successfully");
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in MainWindow constructor");
            _statusManager?.HandleError("Failed to initialize MainWindow", ex);
            throw;
        }
    }

    private void SetupKeyboardShortcuts()
    {
        // Keyboard shortcuts temporarily disabled due to KeyEventArgs ambiguity
        // TODO: Re-implement with proper namespace resolution
    }

    private async void InitializeBrowser()
    {
        try
        {
            // Ensure WebView2 runtime is available
            await Browser.EnsureCoreWebView2Async();

            // Set up browser event handlers
            Browser.NavigationCompleted += Browser_NavigationCompleted;
            Browser.SourceChanged += Browser_SourceChanged;

            // Initialize Spotify login detection
            _loginDetector = new SpotifyLoginDetector(Browser.CoreWebView2);
            _loginDetector.LoginStatusChanged += OnSpotifyLoginStatusChanged;

            // Initialize Spotify automation
            _spotifyAutomation = new SpotifyAutomation(Browser.CoreWebView2);

            // Start monitoring Spotify login status
            await _loginDetector.StartMonitoringAsync(30); // Check every 30 seconds

            // Navigate to Spotify by default
            Browser.Source = new Uri("https://open.spotify.com");

            Logger.Info("MainWindow", "WebView2 initialized successfully with built-in codec support!");
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error initializing WebView2");
            _statusManager?.HandleError("Failed to initialize WebView2", ex);
            throw;
        }
    }

    private async void Browser_NavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        // Navigation completed - log for debugging and check login status
        await Dispatcher.InvokeAsync(async () =>
        {
            if (e.IsSuccess)
            {
                Logger.Debug("MainWindow", "Navigation completed successfully");

                // Check Spotify login status after navigation
                if (_loginDetector != null && Browser.Source != null)
                {
                    await _loginDetector.OnNavigationCompletedAsync(Browser.Source.ToString());
                }

                // Refresh audio sessions after navigation as new audio might start
                _audioManager?.RefreshAudioSession();
            }
            else
            {
                Logger.Warning("MainWindow", "Navigation failed");
                _statusManager?.HandleError("Navigation failed");
            }
        });
    }

    private void Browser_SourceChanged(object? sender, CoreWebView2SourceChangedEventArgs e)
    {
        // Source changed - log for debugging
        Dispatcher.Invoke(() =>
        {
            Logger.Debug("MainWindow", $"Browser navigated to: {Browser.Source?.ToString() ?? ""}");
        });
    }

    /// <summary>
    /// Handles Spotify login status changes
    /// </summary>
    private void OnSpotifyLoginStatusChanged(object? sender, SpotifyLoginStatusChangedEventArgs e)
    {
        Dispatcher.Invoke(() =>
        {
            try
            {
                Logger.Info("MainWindow", $"Spotify login status changed: {e.Status}");
                _statusManager?.UpdateSpotifyStatus(e.Status);

                // Update status text in the UI
                if (StatusText != null)
                {
                    StatusText.Text = _statusManager?.GetStatusMessage() ?? "Unknown Status";
                }
            }
            catch (Exception ex)
            {
                Logger.Error("MainWindow", ex, "Error handling Spotify login status change");
                _statusManager?.HandleError("Failed to handle login status change", ex);
            }
        });
    }

    /// <summary>
    /// Initializes the UI state after all components are loaded
    /// </summary>
    private void InitializeUIState()
    {
        try
        {
            Logger.Debug("MainWindow", "Initializing UI state...");

            // Ensure the mute button exists before updating it
            if (MuteButton == null)
            {
                Logger.Warning("MainWindow", "MuteButton is null during UI initialization - retrying...");
                Dispatcher.BeginInvoke(() => InitializeUIState());
                return;
            }

            // Update the mute button state based on saved preferences
            UpdateMuteButtonState();

            // Log the initial state for debugging
            if (_audioManager != null)
            {
                Logger.Debug("MainWindow", $"Initial UI state set - Mute: {_audioManager.IsMuted}");
            }

            Logger.Debug("MainWindow", "UI state initialization completed");
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error initializing UI state");
        }
    }

    /// <summary>
    /// Handles mute state changes from the AudioManager
    /// </summary>
    private void OnMuteStateChanged(object? sender, MuteStateChangedEventArgs e)
    {
        // Use BeginInvoke to avoid blocking the audio thread
        Dispatcher.BeginInvoke(() =>
        {
            try
            {
                Logger.Debug("MainWindow", $"Mute state change event received: {e.IsMuted}");
                UpdateMuteButtonState();
                Logger.Debug("MainWindow", "UI updated from mute state change event");
            }
            catch (Exception ex)
            {
                Logger.Error("MainWindow", ex, "Error handling mute state change");
            }
        });
    }

    /// <summary>
    /// Updates the mute button appearance based on current mute state
    /// </summary>
    private void UpdateMuteButtonState()
    {
        try
        {
            // Ensure we're on the UI thread
            if (!Dispatcher.CheckAccess())
            {
                Dispatcher.BeginInvoke(() => UpdateMuteButtonState());
                return;
            }

            if (_audioManager == null)
            {
                Logger.Warning("MainWindow", "Cannot update mute button: AudioManager is null");
                return;
            }

            if (MuteButton == null)
            {
                Logger.Warning("MainWindow", "Cannot update mute button: MuteButton is null");
                return;
            }

            var isMuted = _audioManager.IsMuted;
            Logger.Debug("MainWindow", $"Updating mute button UI: isMuted={isMuted}");

            // Store current state for comparison
            var oldContent = MuteButton.Content?.ToString();
            var oldTooltip = MuteButton.ToolTip?.ToString();

            // Update button content and tooltip
            var newContent = isMuted ? "🔇" : "🔊";
            var newTooltip = isMuted ? "Unmute Audio (Ctrl+M)" : "Mute Audio (Ctrl+M)";

            MuteButton.Content = newContent;
            MuteButton.ToolTip = newTooltip;

            // Update button background color
            var newBackground = isMuted ?
                new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x8B, 0x00, 0x00)) : // Dark red when muted
                new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x3C, 0x3C, 0x3C));   // Normal gray

            MuteButton.Background = newBackground;

            // Log the changes
            if (oldContent != newContent || oldTooltip != newTooltip)
            {
                Logger.Debug("MainWindow", $"Mute button updated: Content: '{oldContent}' → '{newContent}', Tooltip: '{oldTooltip}' → '{newTooltip}', Background: {(isMuted ? "Dark Red (Muted)" : "Gray (Unmuted)")}");
            }
            else
            {
                Logger.Debug("MainWindow", $"Mute button already in correct state: {newContent}");
            }

            // Force UI refresh
            MuteButton.InvalidateVisual();
            MuteButton.UpdateLayout();
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error updating mute button state");
        }
    }

    /// <summary>
    /// Centralized mute toggle with rate limiting and visual feedback
    /// </summary>
    private void PerformMuteToggle(string source)
    {
        try
        {
            var now = DateTime.Now;
            var timeSinceLastToggle = now - _lastMuteToggleTime;

            // Check if we're in cooldown period
            if (timeSinceLastToggle < _muteToggleCooldown)
            {
                var remainingCooldown = _muteToggleCooldown - timeSinceLastToggle;
                Logger.Debug("MainWindow", $"Mute toggle from {source} ignored - cooldown active ({remainingCooldown.TotalMilliseconds:F0}ms remaining)");

                // Show visual feedback that the action was ignored
                ShowCooldownFeedback();
                return;
            }

            // Check if another mute operation is in progress
            if (_isMuteOperationInProgress)
            {
                Logger.Debug("MainWindow", $"Mute toggle from {source} ignored - operation already in progress");
                return;
            }

            if (_audioManager == null)
            {
                Logger.Error("MainWindow", "AudioManager is null!");
                return;
            }

            // Start the mute operation
            _isMuteOperationInProgress = true;
            _lastMuteToggleTime = now;

            Logger.Debug("MainWindow", $"Starting mute toggle from {source}");

            // Show loading state
            ShowMuteOperationInProgress();

            var oldState = _audioManager.IsMuted;
            Logger.Debug("MainWindow", $"{source} - Current mute state before toggle: {oldState}");

            // Perform the toggle operation asynchronously to avoid blocking UI
            Task.Run(async () =>
            {
                try
                {
                    // Toggle the mute state
                    _audioManager.ToggleMute();

                    var newState = _audioManager.IsMuted;
                    Logger.Debug("MainWindow", $"{source} - Current mute state after toggle: {newState}");

                    // Update UI on the UI thread
                    await Dispatcher.BeginInvoke(() =>
                    {
                        try
                        {
                            UpdateMuteButtonState();

                            // Verify the state change occurred
                            if (oldState != newState)
                            {
                                Logger.Info("MainWindow", $"Mute state successfully changed via {source}: {oldState} → {newState}");
                            }
                            else
                            {
                                Logger.Warning("MainWindow", $"Mute state did not change via {source} - may indicate an issue");
                            }
                        }
                        finally
                        {
                            // Clear the in-progress flag and restore normal button state
                            _isMuteOperationInProgress = false;
                            RestoreNormalButtonState();
                        }
                    });
                }
                catch (Exception ex)
                {
                    Logger.Error("MainWindow", ex, $"Error in mute toggle from {source}");

                    // Restore UI state on error
                    await Dispatcher.BeginInvoke(() =>
                    {
                        try
                        {
                            UpdateMuteButtonState();
                        }
                        catch (Exception uiEx)
                        {
                            Logger.Error("MainWindow", uiEx, "Failed to update UI after error");
                        }
                        finally
                        {
                            _isMuteOperationInProgress = false;
                            RestoreNormalButtonState();
                        }
                    });
                }
            });
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, $"Error in PerformMuteToggle from {source}");
            _isMuteOperationInProgress = false;
            RestoreNormalButtonState();
        }
    }

    /// <summary>
    /// Shows visual feedback when a mute toggle is ignored due to cooldown
    /// </summary>
    private void ShowCooldownFeedback()
    {
        if (MuteButton == null) return;

        try
        {
            // Briefly flash the button to indicate the action was ignored
            var originalOpacity = MuteButton.Opacity;
            MuteButton.Opacity = 0.5;

            Task.Delay(200).ContinueWith(_ =>
            {
                Dispatcher.BeginInvoke(() =>
                {
                    if (MuteButton != null)
                    {
                        MuteButton.Opacity = originalOpacity;
                    }
                });
            });
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error showing cooldown feedback");
        }
    }

    /// <summary>
    /// Shows that a mute operation is in progress
    /// </summary>
    private void ShowMuteOperationInProgress()
    {
        if (MuteButton == null) return;

        try
        {
            MuteButton.IsEnabled = false;
            MuteButton.Opacity = 0.7;
            Logger.Debug("MainWindow", "Button disabled - mute operation in progress");
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error showing operation in progress");
        }
    }

    /// <summary>
    /// Restores normal button state after mute operation completes
    /// </summary>
    private void RestoreNormalButtonState()
    {
        if (MuteButton == null) return;

        try
        {
            MuteButton.IsEnabled = true;
            MuteButton.Opacity = 1.0;
            Logger.Debug("MainWindow", "Button restored to normal state");
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error restoring button state");
        }
    }

    private void MuteButton_Click(object sender, RoutedEventArgs e)
    {
        Logger.Debug("MainWindow", "Mute button clicked!");
        PerformMuteToggle("button click");
    }

    private async void DevToolsButton_Click(object sender, RoutedEventArgs e)
    {
        await ToggleDevTools();
    }

    private void CloseDevToolsButton_Click(object sender, RoutedEventArgs e)
    {
        HideDevTools();
    }

    public async Task ToggleDevTools()
    {
        if (isDevToolsVisible)
        {
            HideDevTools();
        }
        else
        {
            await ShowDevTools();
        }
    }

    private async Task ShowDevTools()
    {
        try
        {
            // Show the dev tools panel
            DevToolsColumn.Width = new GridLength(400);
            DevToolsSplitter.Visibility = Visibility.Visible;
            DevToolsPanel.Visibility = Visibility.Visible;
            isDevToolsVisible = true;

            // Initialize the dev tools WebView if not already done
            if (DevToolsWebView.CoreWebView2 == null)
            {
                await DevToolsWebView.EnsureCoreWebView2Async();
            }

            // Navigate to a simple dev tools interface
            var devToolsHtml = @"
<!DOCTYPE html>
<html>
<head>
    <title>Developer Tools</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #1e1e1e;
            color: #cccccc;
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #2d2d30;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #569cd6;
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 3px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #005a9e;
        }
        #console {
            background-color: #0c0c0c;
            color: #cccccc;
            padding: 10px;
            border-radius: 3px;
            font-family: 'Consolas', monospace;
            height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class='section'>
        <h3>Developer Tools</h3>
        <button onclick='openRealDevTools()'>Open Browser DevTools</button>
        <button onclick='reloadPage()'>Reload Page</button>
        <button onclick='clearConsole()'>Clear Console</button>
    </div>

    <div class='section'>
        <h3>Console Output</h3>
        <div id='console'></div>
    </div>

    <script>
        function openRealDevTools() {
            window.chrome.webview.postMessage(JSON.stringify({action: 'openDevTools'}));
        }

        function reloadPage() {
            window.chrome.webview.postMessage(JSON.stringify({action: 'reload'}));
        }

        function clearConsole() {
            document.getElementById('console').textContent = '';
        }

        // Listen for console messages from the main page
        window.chrome.webview.addEventListener('message', function(event) {
            const console = document.getElementById('console');
            console.textContent += new Date().toLocaleTimeString() + ': ' + event.data + '\n';
            console.scrollTop = console.scrollHeight;
        });

        // Initial message
        document.getElementById('console').textContent = 'Developer Tools initialized.\n';
    </script>
</body>
</html>";

            DevToolsWebView.NavigateToString(devToolsHtml);

            // Set up message handling for dev tools communication
            if (DevToolsWebView.CoreWebView2 != null)
            {
                DevToolsWebView.CoreWebView2.WebMessageReceived += DevToolsWebView_WebMessageReceived;
            }

            Logger.Debug("MainWindow", "Developer tools panel opened");
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error showing dev tools");
        }
    }

    private void DevToolsWebView_WebMessageReceived(object? sender, CoreWebView2WebMessageReceivedEventArgs e)
    {
        try
        {
            var message = e.TryGetWebMessageAsString();
            if (string.IsNullOrEmpty(message))
                return;

            var messageObj = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(message);

            if (messageObj != null && messageObj.TryGetValue("action", out var actionElement))
            {
                var action = actionElement.GetString();
                switch (action)
                {
                    case "openDevTools":
                        if (Browser.CoreWebView2 != null)
                        {
                            Browser.CoreWebView2.OpenDevToolsWindow();
                        }
                        break;
                    case "reload":
                        Browser.Reload();
                        break;
                }
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error handling dev tools message");
        }
    }

    private void HideDevTools()
    {
        DevToolsColumn.Width = new GridLength(0);
        DevToolsSplitter.Visibility = Visibility.Collapsed;
        DevToolsPanel.Visibility = Visibility.Collapsed;
        isDevToolsVisible = false;
        Logger.Debug("MainWindow", "Developer tools panel closed");
    }



    protected override void OnStateChanged(EventArgs e)
    {
        // Minimize to tray when window is minimized
        if (WindowState == WindowState.Minimized)
        {
            this.Hide();
        }
        base.OnStateChanged(e);
    }

    protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
    {
        // Dispose status manager and audio manager resources
        _statusManager?.Dispose();
        _audioManager?.Dispose();
        base.OnClosing(e);
    }

    protected override void OnClosed(EventArgs e)
    {
        _statusManager?.Dispose();
        _audioManager?.Dispose();
        base.OnClosed(e);
    }

    #region Spotify Automation Methods

    /// <summary>
    /// Creates a new playlist with the specified name
    /// </summary>
    /// <param name="playlistName">Name for the new playlist</param>
    /// <returns>True if playlist creation appears successful</returns>
    public async Task<bool> SpotifyCreatePlaylistAsync(string playlistName)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", $"Creating Spotify playlist: {playlistName}");
        return await _spotifyAutomation.CreatePlaylistAsync(playlistName);
    }

    /// <summary>
    /// Navigates to the Spotify dashboard
    /// </summary>
    /// <returns>True if navigation appears successful</returns>
    public async Task<bool> SpotifyGoToDashboardAsync()
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", "Navigating to Spotify dashboard");
        return await _spotifyAutomation.GoToDashboardAsync();
    }

    /// <summary>
    /// Checks if a playlist with the given name exists
    /// </summary>
    /// <param name="playlistName">Name of the playlist to check</param>
    /// <returns>True if playlist exists</returns>
    public async Task<bool> SpotifyPlaylistExistsAsync(string playlistName)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", $"Checking if Spotify playlist exists: {playlistName}");
        return await _spotifyAutomation.PlaylistExistsAsync(playlistName);
    }

    /// <summary>
    /// Deletes a playlist by name using the dashboard interface
    /// </summary>
    /// <param name="playlistName">Name of the playlist to delete</param>
    /// <returns>True if playlist deletion appears successful</returns>
    public async Task<bool> SpotifyDeletePlaylistAsync(string playlistName)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", $"Deleting Spotify playlist: {playlistName}");
        return await _spotifyAutomation.DeletePlaylistAsync(playlistName);
    }

    /// <summary>
    /// Searches for a song or artist on Spotify
    /// </summary>
    /// <param name="searchTerm">What to search for</param>
    /// <returns>True if search was executed</returns>
    public async Task<bool> SpotifySearchAsync(string searchTerm)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", $"Searching Spotify for: {searchTerm}");
        return await _spotifyAutomation.SearchAsync(searchTerm);
    }



    /// <summary>
    /// Gets the currently playing song information
    /// </summary>
    /// <returns>Song info or null if not available</returns>
    public async Task<string?> SpotifyGetCurrentSongAsync()
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return null;
        }

        return await _spotifyAutomation.GetCurrentSongAsync();
    }





    /// <summary>
    /// Adds a song to a Spotify playlist
    /// </summary>
    /// <param name="playlistId">The playlist ID to add the song to</param>
    /// <param name="trackUri">The Spotify track URI (e.g., "spotify:track:4iV5W9uYEdYUVa79Axb7Rh")</param>
    /// <returns>True if song was added successfully</returns>
    public async Task<bool> SpotifyAddSongToPlaylistAsync(string playlistId, string trackUri)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", $"Adding song {trackUri} to playlist {playlistId}");
        return await _spotifyAutomation.AddSongToPlaylistAsync(playlistId, trackUri);
    }

    /// <summary>
    /// Gets the track URI from the first search result
    /// </summary>
    /// <returns>Track URI if found, null otherwise</returns>
    public async Task<string?> SpotifyGetFirstSearchResultTrackUriAsync()
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return null;
        }

        Logger.Info("MainWindow", "Getting track URI from first search result");
        return await _spotifyAutomation.GetFirstSearchResultTrackUriAsync();
    }

    /// <summary>
    /// Gets the current playlist ID from the URL
    /// </summary>
    /// <returns>Playlist ID if found, null otherwise</returns>
    public async Task<string?> SpotifyGetCurrentPlaylistIdAsync()
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return null;
        }

        Logger.Info("MainWindow", "Getting current playlist ID");
        return await _spotifyAutomation.GetCurrentPlaylistIdAsync();
    }

    /// <summary>
    /// Searches for a song and adds the first result to a playlist
    /// </summary>
    /// <param name="searchTerm">What to search for</param>
    /// <param name="playlistId">The playlist ID to add the song to</param>
    /// <returns>True if song was found and added successfully</returns>
    public async Task<bool> SpotifySearchAndAddSongToPlaylistAsync(string searchTerm, string playlistId)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", $"Searching for '{searchTerm}' and adding to playlist {playlistId}");
        return await _spotifyAutomation.SearchAndAddSongToPlaylistAsync(searchTerm, playlistId);
    }

    /// <summary>
    /// Searches for a song with validation that results appear
    /// </summary>
    /// <param name="searchTerm">What to search for</param>
    /// <returns>True if search was executed and results were validated</returns>
    public async Task<bool> SpotifySearchWithValidationAsync(string searchTerm)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", $"Searching with validation for: {searchTerm}");
        return await _spotifyAutomation.SearchWithValidationAsync(searchTerm);
    }

    /// <summary>
    /// Plays a playlist by navigating to it and clicking the play button
    /// </summary>
    /// <param name="playlistName">Name of the playlist to play</param>
    /// <returns>True if playlist was found and play button was clicked successfully</returns>
    public async Task<bool> SpotifyPlayPlaylistAsync(string playlistName)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", $"Playing playlist: {playlistName}");
        return await _spotifyAutomation.PlayPlaylistAsync(playlistName);
    }

    /// <summary>
    /// Lists all songs in a playlist by navigating to it and extracting the tracklist
    /// </summary>
    /// <param name="playlistName">Name of the playlist to list songs from</param>
    /// <returns>List of song names or empty list if failed</returns>
    public async Task<List<string>> SpotifyListPlaylistSongsAsync(string playlistName)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return new List<string>();
        }

        Logger.Info("MainWindow", $"Listing songs in playlist: {playlistName}");
        return await _spotifyAutomation.ListPlaylistSongsAsync(playlistName);
    }

    /// <summary>
    /// Searches for a song and adds it to a playlist by name using the exact Spotify UI workflow
    /// </summary>
    /// <param name="songName">Name of the song to search for</param>
    /// <param name="playlistName">Name of the playlist to add the song to</param>
    /// <returns>True if song was found and added successfully</returns>
    public async Task<bool> SpotifySearchAndAddSongToPlaylistByNameAsync(string songName, string playlistName)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", $"Searching for '{songName}' and adding to playlist '{playlistName}' using exact UI workflow");
        return await _spotifyAutomation.SearchAndAddSongToPlaylistByNameAsync(songName, playlistName);
    }

    /// <summary>
    /// Removes a song from a playlist by name using the exact Spotify UI workflow
    /// </summary>
    /// <param name="songName">Name of the song to remove</param>
    /// <param name="playlistName">Name of the playlist to remove the song from</param>
    /// <returns>True if song was found and removed successfully</returns>
    public async Task<bool> SpotifyRemoveSongFromPlaylistByNameAsync(string songName, string playlistName)
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", $"Removing song '{songName}' from playlist '{playlistName}' using exact UI workflow");
        return await _spotifyAutomation.RemoveSongFromPlaylistByNameAsync(songName, playlistName);
    }

    /// <summary>
    /// Tests if the dashboard navigation was successful
    /// </summary>
    /// <returns>True if dashboard elements are found</returns>
    public async Task<bool> SpotifyTestDashboardNavigationAsync()
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", "Testing dashboard navigation");
        return await _spotifyAutomation.TestDashboardNavigationAsync();
    }

    /// <summary>
    /// Comprehensive test that navigates to dashboard and verifies success
    /// </summary>
    /// <returns>True if navigation and verification both succeed</returns>
    public async Task<bool> SpotifyTestGoToDashboardAsync()
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", "Running comprehensive dashboard navigation test");
        return await _spotifyAutomation.TestGoToDashboardAsync();
    }

    /// <summary>
    /// Gets debug information about buttons on the current page
    /// </summary>
    /// <returns>Debug information about buttons</returns>
    public async Task<string> SpotifyGetPageButtonInfoAsync()
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return "Spotify automation not initialized";
        }

        Logger.Info("MainWindow", "Getting page button debug info");
        return await _spotifyAutomation.GetPageButtonInfoAsync();
    }

    /// <summary>
    /// Tests the visual debugging and hovering system (only works in debug mode)
    /// </summary>
    /// <returns>True if all tests pass</returns>
    public async Task<bool> SpotifyTestVisualDebuggingAsync()
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", "Testing Spotify visual debugging system");
        return await _spotifyAutomation.TestVisualDebuggingSystemAsync();
    }

    /// <summary>
    /// Enables regular Shuffle for the current playlist by cycling through shuffle states
    /// </summary>
    /// <returns>True if regular Shuffle was successfully enabled</returns>
    public async Task<bool> SpotifyEnableShuffleAsync()
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", "Enabling regular Shuffle for current playlist");
        return await _spotifyAutomation.EnableShuffleAsync();
    }

    /// <summary>
    /// Enables Repeat mode for the current playlist by cycling through repeat states
    /// </summary>
    /// <returns>True if Repeat mode was successfully enabled</returns>
    public async Task<bool> SpotifyEnableRepeatAsync()
    {
        if (_spotifyAutomation == null)
        {
            Logger.Error("MainWindow", "Spotify automation not initialized");
            return false;
        }

        Logger.Info("MainWindow", "Enabling Repeat mode for current playlist");
        return await _spotifyAutomation.EnableRepeatAsync();
    }

    /// <summary>
    /// Executes custom JavaScript in the WebView2 for advanced automation
    /// </summary>
    /// <param name="script">JavaScript code to execute</param>
    /// <returns>Result of the script execution</returns>
    public async Task<string?> ExecuteCustomScriptAsync(string script)
    {
        try
        {
            if (Browser.CoreWebView2 == null)
            {
                Logger.Error("MainWindow", "WebView2 not initialized");
                return null;
            }

            Logger.Debug("MainWindow", "Executing custom script...");
            var result = await Browser.CoreWebView2.ExecuteScriptAsync(script);
            Logger.Debug("MainWindow", $"Script result: {result}");
            return result;
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error executing custom script");
            return null;
        }
    }

    #endregion

    #region UI Event Handlers for Automation

    private async void CreatePlaylistButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var playlistName = PlaylistNameTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(playlistName))
            {
                Logger.Warning("MainWindow", "Playlist name is empty");
                _statusManager?.HandleError("Please enter a playlist name");
                return;
            }

            Logger.Info("MainWindow", $"Creating playlist via UI button: {playlistName}");
            StatusText.Text = $"Creating playlist '{playlistName}'...";

            var success = await SpotifyCreatePlaylistAsync(playlistName);

            if (success)
            {
                StatusText.Text = $"Playlist '{playlistName}' created successfully!";
                // Clear the text box for next use
                PlaylistNameTextBox.Text = "My New Playlist";
            }
            else
            {
                StatusText.Text = $"Failed to create playlist '{playlistName}'";
                _statusManager?.HandleError($"Failed to create playlist '{playlistName}'");
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in CreatePlaylistButton_Click");
            _statusManager?.HandleError("Error creating playlist", ex);
        }
    }

    private async void DeletePlaylistButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var playlistName = PlaylistNameTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(playlistName))
            {
                Logger.Warning("MainWindow", "Playlist name is empty");
                StatusText.Text = "Please enter a playlist name to delete";
                return;
            }

            Logger.Info("MainWindow", $"Checking if playlist exists: {playlistName}");
            StatusText.Text = $"Checking if playlist '{playlistName}' exists...";

            // First check if playlist exists
            var exists = await SpotifyPlaylistExistsAsync(playlistName);
            if (!exists)
            {
                StatusText.Text = $"Playlist '{playlistName}' not found";
                Logger.Warning("MainWindow", $"Playlist '{playlistName}' does not exist");
                return;
            }

            // No confirmation dialog - proceed directly with deletion

            Logger.Info("MainWindow", $"Deleting playlist via UI button: {playlistName}");
            StatusText.Text = $"Deleting playlist '{playlistName}'...";

            var success = await SpotifyDeletePlaylistAsync(playlistName);

            if (success)
            {
                StatusText.Text = $"Playlist '{playlistName}' deleted successfully!";
                // Clear the text box for next use
                PlaylistNameTextBox.Text = "My New Playlist";
            }
            else
            {
                StatusText.Text = $"Failed to delete playlist '{playlistName}'";
                _statusManager?.HandleError($"Failed to delete playlist '{playlistName}'");
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in DeletePlaylistButton_Click");
            StatusText.Text = "Error deleting playlist";
            _statusManager?.HandleError("Error deleting playlist", ex);
        }
    }

    private async void SearchButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var searchTerm = SearchTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(searchTerm) || searchTerm == "Search for songs...")
            {
                Logger.Warning("MainWindow", "Search term is empty");
                _statusManager?.HandleError("Please enter a search term");
                return;
            }

            Logger.Info("MainWindow", $"Searching via UI button: {searchTerm}");
            StatusText.Text = $"Searching for '{searchTerm}'...";

            var success = await SpotifySearchAsync(searchTerm);

            if (success)
            {
                StatusText.Text = $"Search completed for '{searchTerm}'";
            }
            else
            {
                StatusText.Text = $"Failed to search for '{searchTerm}'";
                _statusManager?.HandleError($"Failed to search for '{searchTerm}'");
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in SearchButton_Click");
            _statusManager?.HandleError("Error performing search", ex);
        }
    }



    private async void DashboardButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            Logger.Info("MainWindow", "Navigating to dashboard via UI button");
            StatusText.Text = "Navigating to dashboard...";

            var success = await SpotifyGoToDashboardAsync();

            if (success)
            {
                StatusText.Text = "Successfully navigated to dashboard";
            }
            else
            {
                StatusText.Text = "Failed to navigate to dashboard";
                _statusManager?.HandleError("Failed to navigate to dashboard");
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in DashboardButton_Click");
            StatusText.Text = "Error navigating to dashboard";
            _statusManager?.HandleError("Error navigating to dashboard", ex);
        }
    }

    private async void AddSongButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var songName = SongNameTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(songName) || songName == "Song name...")
            {
                Logger.Warning("MainWindow", "Song name is empty");
                _statusManager?.HandleError("Please enter a song name");
                return;
            }

            var playlistName = TargetPlaylistTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(playlistName) || playlistName == "Playlist name...")
            {
                Logger.Warning("MainWindow", "Playlist name is empty");
                _statusManager?.HandleError("Please enter a playlist name");
                return;
            }

            Logger.Info("MainWindow", $"Adding song '{songName}' to playlist '{playlistName}' via UI button");
            StatusText.Text = $"Adding '{songName}' to '{playlistName}'...";

            var success = await SpotifySearchAndAddSongToPlaylistByNameAsync(songName, playlistName);

            if (success)
            {
                StatusText.Text = $"Successfully added '{songName}' to '{playlistName}'!";
                Logger.Info("MainWindow", $"Successfully added song '{songName}' to playlist '{playlistName}'");
            }
            else
            {
                StatusText.Text = $"Failed to add '{songName}' to '{playlistName}'";
                Logger.Warning("MainWindow", $"Failed to add song '{songName}' to playlist '{playlistName}'");
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in AddSongButton_Click");
            StatusText.Text = "Error adding song to playlist";
            _statusManager?.HandleError("Error adding song to playlist", ex);
        }
    }

    private async void RemoveSongButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var songName = RemoveSongNameTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(songName) || songName == "Song to remove...")
            {
                Logger.Warning("MainWindow", "Song name is empty");
                _statusManager?.HandleError("Please enter a song name to remove");
                return;
            }

            var playlistName = RemoveFromPlaylistTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(playlistName) || playlistName == "From playlist...")
            {
                Logger.Warning("MainWindow", "Playlist name is empty");
                _statusManager?.HandleError("Please enter a playlist name");
                return;
            }

            Logger.Info("MainWindow", $"Removing song '{songName}' from playlist '{playlistName}' via UI button");
            StatusText.Text = $"Removing '{songName}' from '{playlistName}'...";

            var success = await SpotifyRemoveSongFromPlaylistByNameAsync(songName, playlistName);

            if (success)
            {
                StatusText.Text = $"Successfully removed '{songName}' from '{playlistName}'!";
                Logger.Info("MainWindow", $"Successfully removed song '{songName}' from playlist '{playlistName}'");
            }
            else
            {
                StatusText.Text = $"Failed to remove '{songName}' from '{playlistName}'";
                Logger.Warning("MainWindow", $"Failed to remove song '{songName}' from playlist '{playlistName}'");
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in RemoveSongButton_Click");
            StatusText.Text = "Error removing song from playlist";
            _statusManager?.HandleError("Error removing song from playlist", ex);
        }
    }





    private void PlaylistNameTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        if (PlaylistNameTextBox.Text == "My New Playlist")
        {
            PlaylistNameTextBox.Text = "";
            PlaylistNameTextBox.Foreground = new SolidColorBrush(Colors.White);
        }
    }

    private void PlaylistNameTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(PlaylistNameTextBox.Text))
        {
            PlaylistNameTextBox.Text = "My New Playlist";
            PlaylistNameTextBox.Foreground = new SolidColorBrush(Colors.Gray);
        }
    }

    private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        if (SearchTextBox.Text == "Search for songs...")
        {
            SearchTextBox.Text = "";
            SearchTextBox.Foreground = new SolidColorBrush(Colors.White);
        }
    }

    private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
        {
            SearchTextBox.Text = "Search for songs...";
            SearchTextBox.Foreground = new SolidColorBrush(Colors.Gray);
        }
    }

    private void SongNameTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        if (SongNameTextBox.Text == "brisa metamorfose")
        {
            SongNameTextBox.Text = "";
            SongNameTextBox.Foreground = new SolidColorBrush(Colors.White);
        }
    }

    private void SongNameTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(SongNameTextBox.Text))
        {
            SongNameTextBox.Text = "brisa metamorfose";
            SongNameTextBox.Foreground = new SolidColorBrush(Colors.Gray);
        }
    }

    private void TargetPlaylistTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        if (TargetPlaylistTextBox.Text == "Hello")
        {
            TargetPlaylistTextBox.Text = "";
            TargetPlaylistTextBox.Foreground = new SolidColorBrush(Colors.White);
        }
    }

    private void TargetPlaylistTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(TargetPlaylistTextBox.Text))
        {
            TargetPlaylistTextBox.Text = "Hello";
            TargetPlaylistTextBox.Foreground = new SolidColorBrush(Colors.Gray);
        }
    }

    private void RemoveSongNameTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        if (RemoveSongNameTextBox.Text == "Song to remove...")
        {
            RemoveSongNameTextBox.Text = "";
            RemoveSongNameTextBox.Foreground = new SolidColorBrush(Colors.White);
        }
    }

    private void RemoveSongNameTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(RemoveSongNameTextBox.Text))
        {
            RemoveSongNameTextBox.Text = "Song to remove...";
            RemoveSongNameTextBox.Foreground = new SolidColorBrush(Colors.Gray);
        }
    }

    private void RemoveFromPlaylistTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        if (RemoveFromPlaylistTextBox.Text == "From playlist...")
        {
            RemoveFromPlaylistTextBox.Text = "";
            RemoveFromPlaylistTextBox.Foreground = new SolidColorBrush(Colors.White);
        }
    }

    private void RemoveFromPlaylistTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(RemoveFromPlaylistTextBox.Text))
        {
            RemoveFromPlaylistTextBox.Text = "From playlist...";
            RemoveFromPlaylistTextBox.Foreground = new SolidColorBrush(Colors.Gray);
        }
    }

    private void QuickPlayPlaylistTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        if (QuickPlayPlaylistTextBox.Text == "Hello")
        {
            QuickPlayPlaylistTextBox.Text = "";
            QuickPlayPlaylistTextBox.Foreground = new SolidColorBrush(Colors.White);
        }
    }

    private void QuickPlayPlaylistTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(QuickPlayPlaylistTextBox.Text))
        {
            QuickPlayPlaylistTextBox.Text = "Hello";
            QuickPlayPlaylistTextBox.Foreground = new SolidColorBrush(Colors.Gray);
        }
    }

    private void ListSongsPlaylistTextBox_GotFocus(object sender, RoutedEventArgs e)
    {
        if (ListSongsPlaylistTextBox.Text == "Hello")
        {
            ListSongsPlaylistTextBox.Text = "";
            ListSongsPlaylistTextBox.Foreground = new SolidColorBrush(Colors.White);
        }
    }

    private void ListSongsPlaylistTextBox_LostFocus(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(ListSongsPlaylistTextBox.Text))
        {
            ListSongsPlaylistTextBox.Text = "Hello";
            ListSongsPlaylistTextBox.Foreground = new SolidColorBrush(Colors.Gray);
        }
    }

    // Temporarily commented out due to KeyEventArgs ambiguity
    // private void QuickPlayPlaylistTextBox_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    // {
    //     if (e.Key == System.Windows.Input.Key.Enter)
    //     {
    //         // Trigger the Quick Play button click
    //         QuickPlayButton_Click(sender, new RoutedEventArgs());
    //     }
    // }

    private async void QuickPlayButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var playlistName = QuickPlayPlaylistTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(playlistName))
            {
                StatusText.Text = "Please enter a playlist name";
                Logger.Warning("MainWindow", "Quick Play playlist name is empty");
                _statusManager?.HandleError("Please enter a playlist name");
                return;
            }

            Logger.Info("MainWindow", $"Quick playing playlist: {playlistName}");
            StatusText.Text = $"🎵 Quick playing '{playlistName}'...";

            var success = await SpotifyPlayPlaylistAsync(playlistName);

            if (success)
            {
                StatusText.Text = $"🎵 '{playlistName}' is now playing!";
                Logger.Info("MainWindow", $"Successfully quick played playlist '{playlistName}'");

                // Optionally clear the text box for next use
                QuickPlayPlaylistTextBox.Text = "Hello";
                QuickPlayPlaylistTextBox.Foreground = new SolidColorBrush(Colors.Gray);
            }
            else
            {
                StatusText.Text = $"❌ Failed to play playlist '{playlistName}'";
                Logger.Warning("MainWindow", $"Failed to quick play playlist '{playlistName}'");
                _statusManager?.HandleError($"Failed to play playlist '{playlistName}'");
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in QuickPlayButton_Click");
            StatusText.Text = "❌ Error playing playlist";
            _statusManager?.HandleError("Error playing playlist", ex);
        }
    }

    private async void ListSongsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var playlistName = ListSongsPlaylistTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(playlistName))
            {
                StatusText.Text = "Please enter a playlist name to list songs";
                Logger.Warning("MainWindow", "List Songs playlist name is empty");
                _statusManager?.HandleError("Please enter a playlist name to list songs");
                return;
            }

            Logger.Info("MainWindow", $"Listing songs in playlist: {playlistName}");
            StatusText.Text = $"📋 Listing songs in '{playlistName}'...";

            var songs = await SpotifyListPlaylistSongsAsync(playlistName);

            if (songs.Count > 0)
            {
                StatusText.Text = $"📋 Found {songs.Count} songs in '{playlistName}' - check logs for details";
                Logger.Info("MainWindow", $"Successfully retrieved {songs.Count} songs from playlist '{playlistName}':");

                // Log all songs
                for (int i = 0; i < songs.Count; i++)
                {
                    Logger.Info("MainWindow", $"  {i + 1}. {songs[i]}");
                }

                // Optionally clear the text box for next use
                ListSongsPlaylistTextBox.Text = "Hello";
                ListSongsPlaylistTextBox.Foreground = new SolidColorBrush(Colors.Gray);
            }
            else
            {
                StatusText.Text = $"❌ No songs found in playlist '{playlistName}' or playlist not found";
                Logger.Warning("MainWindow", $"No songs found in playlist '{playlistName}'");
                _statusManager?.HandleError($"No songs found in playlist '{playlistName}'");
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in ListSongsButton_Click");
            StatusText.Text = "❌ Error listing playlist songs";
            _statusManager?.HandleError("Error listing playlist songs", ex);
        }
    }

    private async void ShuffleButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            Logger.Info("MainWindow", "Shuffle button clicked");
            StatusText.Text = "🔀 Enabling Shuffle...";

            // Disable button to prevent multiple clicks
            ShuffleButton.IsEnabled = false;
            ShuffleButton.Content = "🔄 Working...";

            var result = await SpotifyEnableShuffleAsync();

            if (result)
            {
                StatusText.Text = "✅ Shuffle enabled successfully!";
                Logger.Info("MainWindow", "Shuffle enabled successfully");
                ShuffleButton.Content = "✅ Shuffled";
                ShuffleButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#2ECC71"));

                // Wait a moment then reset button
                await Task.Delay(2000);
                ShuffleButton.Content = "🔀 Shuffle";
                ShuffleButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#9B59B6"));
            }
            else
            {
                StatusText.Text = "❌ Failed to enable Shuffle";
                Logger.Warning("MainWindow", "Failed to enable Shuffle");
                ShuffleButton.Content = "❌ Failed";
                ShuffleButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#E74C3C"));
                _statusManager?.HandleError("Failed to enable Shuffle");

                // Wait a moment then reset button
                await Task.Delay(2000);
                ShuffleButton.Content = "🔀 Shuffle";
                ShuffleButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#9B59B6"));
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in ShuffleButton_Click");
            StatusText.Text = "❌ Error enabling Shuffle";
            ShuffleButton.Content = "❌ Error";
            ShuffleButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#E74C3C"));
            _statusManager?.HandleError("Error enabling Shuffle", ex);

            // Wait a moment then reset button
            await Task.Delay(2000);
            ShuffleButton.Content = "🔀 Shuffle";
            ShuffleButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#9B59B6"));
        }
        finally
        {
            // Re-enable button
            ShuffleButton.IsEnabled = true;
        }
    }

    private async void RepeatButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            Logger.Info("MainWindow", "Repeat button clicked");
            StatusText.Text = "🔁 Enabling Repeat...";

            // Disable button to prevent multiple clicks
            RepeatButton.IsEnabled = false;
            RepeatButton.Content = "🔄 Working...";

            var result = await SpotifyEnableRepeatAsync();

            if (result)
            {
                StatusText.Text = "✅ Repeat enabled successfully!";
                Logger.Info("MainWindow", "Repeat enabled successfully");
                RepeatButton.Content = "✅ Repeating";
                RepeatButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#27AE60"));

                // Wait a moment then reset button
                await Task.Delay(2000);
                RepeatButton.Content = "🔁 Repeat";
                RepeatButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#E67E22"));
            }
            else
            {
                StatusText.Text = "❌ Failed to enable Repeat";
                Logger.Warning("MainWindow", "Failed to enable Repeat");
                RepeatButton.Content = "❌ Failed";
                RepeatButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#E74C3C"));
                _statusManager?.HandleError("Failed to enable Repeat");

                // Wait a moment then reset button
                await Task.Delay(2000);
                RepeatButton.Content = "🔁 Repeat";
                RepeatButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#E67E22"));
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in RepeatButton_Click");
            StatusText.Text = "❌ Error enabling Repeat";
            RepeatButton.Content = "❌ Error";
            RepeatButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#E74C3C"));
            _statusManager?.HandleError("Error enabling Repeat", ex);

            // Wait a moment then reset button
            await Task.Delay(2000);
            RepeatButton.Content = "🔁 Repeat";
            RepeatButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#E67E22"));
        }
        finally
        {
            // Re-enable button
            RepeatButton.IsEnabled = true;
        }
    }

    private async void ListPlaylistsButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            Logger.Info("MainWindow", "List playlists button clicked");
            StatusText.Text = "📋 Loading playlists...";

            // Disable button to prevent multiple clicks
            ListPlaylistsButton.IsEnabled = false;
            ListPlaylistsButton.Content = "🔄 Loading...";

            if (_spotifyAutomation == null)
            {
                StatusText.Text = "❌ Spotify automation not available";
                Logger.Warning("MainWindow", "Spotify automation is null");
                return;
            }

            var playlistNames = await _spotifyAutomation.ListAllPlaylistsAsync();

            if (playlistNames != null && playlistNames.Count > 0)
            {
                StatusText.Text = $"✅ Found {playlistNames.Count} playlists";
                Logger.Info("MainWindow", $"Successfully retrieved {playlistNames.Count} playlist names");

                // Log all playlist names (this is the core output as requested)
                Logger.Info("MainWindow", "Playlist names extracted:");
                foreach (var name in playlistNames)
                {
                    Logger.Info("MainWindow", $"  - {name}");
                }

                ListPlaylistsButton.Content = "✅ Loaded";
                ListPlaylistsButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#27AE60"));

                // Wait a moment then reset button
                await Task.Delay(2000);
                ListPlaylistsButton.Content = "📋 List Playlists";
                ListPlaylistsButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#8E44AD"));
            }
            else
            {
                StatusText.Text = "❌ No playlists found";
                Logger.Warning("MainWindow", "No playlists found or retrieval failed");
                ListPlaylistsButton.Content = "❌ No playlists";
                ListPlaylistsButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#E74C3C"));

                // Wait a moment then reset button
                await Task.Delay(2000);
                ListPlaylistsButton.Content = "📋 List Playlists";
                ListPlaylistsButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#8E44AD"));
            }
        }
        catch (Exception ex)
        {
            Logger.Error("MainWindow", ex, "Error in ListPlaylistsButton_Click");
            StatusText.Text = "❌ Error loading playlists";
            ListPlaylistsButton.Content = "❌ Error";
            ListPlaylistsButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#E74C3C"));
            _statusManager?.HandleError("Error loading playlists", ex);

            // Wait a moment then reset button
            await Task.Delay(2000);
            ListPlaylistsButton.Content = "📋 List Playlists";
            ListPlaylistsButton.Background = new SolidColorBrush((System.Windows.Media.Color)System.Windows.Media.ColorConverter.ConvertFromString("#8E44AD"));
        }
        finally
        {
            // Re-enable button
            ListPlaylistsButton.IsEnabled = true;
        }
    }

    #endregion
}