using System;
using System.Threading.Tasks;
using System.Windows.Threading;
using Microsoft.Web.WebView2.Core;
using vys.Logging;

namespace vys
{
    /// <summary>
    /// Detects Spotify login status by checking for authentication cookies
    /// </summary>
    public class SpotifyLoginDetector
    {
        private readonly CoreWebView2 _webView;
        private const string SPOTIFY_AUTH_COOKIE = "sp_dc";

        public SpotifyLoginDetector(CoreWebView2 webView)
        {
            _webView = webView ?? throw new ArgumentNullException(nameof(webView));
        }

        /// <summary>
        /// Event fired when Spotify login status changes
        /// </summary>
        public event EventHandler<SpotifyLoginStatusChangedEventArgs>? LoginStatusChanged;

        /// <summary>
        /// Checks if user is logged into Spotify by looking for the sp_dc cookie
        /// </summary>
        /// <returns>Current Spotify login status</returns>
        public async Task<SpotifyLoginStatus> CheckLoginStatusAsync()
        {
            try
            {
                Logger.Debug("SpotifyLoginDetector", "Checking Spotify login status...");

                // Use JavaScript to check for login status instead of cookies (avoids WebView2 version issues)
                var loginCheckScript = @"
                (function() {
                    try {
                        // Check for common Spotify login indicators
                        const userWidget = document.querySelector('[data-testid=""user-widget-link""]');
                        const topBarWidget = document.querySelector('[data-testid=""top-bar-user-widget""]');
                        const profileButton = document.querySelector('[data-testid=""user-widget""]');
                        const avatarButton = document.querySelector('button[data-testid=""user-widget-link""]');

                        // Check if we're on a logged-in page
                        const isOnDashboard = window.location.href.includes('open.spotify.com') &&
                                            !window.location.href.includes('/login') &&
                                            !window.location.href.includes('/signup');

                        // Check for user-specific elements
                        const hasUserElements = userWidget || topBarWidget || profileButton || avatarButton;

                        // Check for login/signup buttons (indicates not logged in)
                        const loginButton = document.querySelector('[data-testid=""login-button""]');
                        const signupButton = document.querySelector('[data-testid=""signup-button""]');
                        const hasLoginButtons = loginButton || signupButton;

                        const isLoggedIn = (hasUserElements || isOnDashboard) && !hasLoginButtons;

                        return {
                            isLoggedIn: isLoggedIn,
                            hasUserElements: !!hasUserElements,
                            isOnDashboard: isOnDashboard,
                            hasLoginButtons: !!hasLoginButtons,
                            currentUrl: window.location.href
                        };
                    } catch (error) {
                        return {
                            isLoggedIn: false,
                            error: error.message
                        };
                    }
                })();";

                // Execute on UI thread
                var resultTask = System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    return await _webView.ExecuteScriptAsync(loginCheckScript);
                });

                var result = await (await resultTask);

                Logger.Debug("SpotifyLoginDetector", $"Login check result: {result}");

                // Parse the result
                bool hasValidAuthCookie = result.Contains("\"isLoggedIn\":true") || result.Contains("\"isLoggedIn\": true");
                string foundCookieInfo = hasValidAuthCookie ? "JavaScript detection: user elements found" : "JavaScript detection: no user elements";

                var status = hasValidAuthCookie ? SpotifyLoginStatus.LoggedIn : SpotifyLoginStatus.NotLoggedIn;

                if (hasValidAuthCookie)
                {
                    Logger.Info("SpotifyLoginDetector", $"Spotify login status: {status} - {foundCookieInfo}");
                }
                else
                {
                    Logger.Info("SpotifyLoginDetector", $"Spotify login status: {status} - No valid sp_dc cookie found");
                }

                // Fire event if status changed
                OnLoginStatusChanged(status);

                return status;
            }
            catch (Exception ex)
            {
                Logger.Error("SpotifyLoginDetector", ex, "Error checking Spotify login status");

                var errorStatus = SpotifyLoginStatus.Error;
                OnLoginStatusChanged(errorStatus);
                return errorStatus;
            }
        }

        /// <summary>
        /// Starts monitoring Spotify login status with periodic checks
        /// </summary>
        /// <param name="intervalSeconds">Check interval in seconds (default: 30)</param>
        public async Task StartMonitoringAsync(int intervalSeconds = 30)
        {
            Logger.Info("SpotifyLoginDetector", $"Starting Spotify login monitoring (interval: {intervalSeconds}s)...");

            // Initial check
            await CheckLoginStatusAsync();

            // Set up periodic monitoring
            var timer = new System.Timers.Timer(intervalSeconds * 1000);
            timer.Elapsed += async (sender, e) =>
            {
                try
                {
                    await CheckLoginStatusAsync();
                }
                catch (Exception ex)
                {
                    Logger.Error("SpotifyLoginDetector", ex, "Error during periodic login check");
                }
            };

            timer.Start();
            Logger.Debug("SpotifyLoginDetector", "Spotify login monitoring started");
        }

        /// <summary>
        /// Checks login status when navigation completes to Spotify domains
        /// </summary>
        /// <param name="uri">The URI that was navigated to</param>
        public async Task OnNavigationCompletedAsync(string uri)
        {
            try
            {
                if (IsSpotifyUrl(uri))
                {
                    Logger.Debug("SpotifyLoginDetector", $"Navigation to Spotify detected: {uri}");
                    // Wait a moment for cookies to be set
                    await Task.Delay(2000);
                    await CheckLoginStatusAsync();
                }
            }
            catch (Exception ex)
            {
                Logger.Error("SpotifyLoginDetector", ex, "Error checking login status after navigation");
            }
        }

        /// <summary>
        /// Checks if the given URL is a Spotify URL
        /// </summary>
        /// <param name="url">URL to check</param>
        /// <returns>True if it's a Spotify URL</returns>
        private static bool IsSpotifyUrl(string url)
        {
            if (string.IsNullOrEmpty(url)) return false;
            
            try
            {
                var uri = new Uri(url);
                return uri.Host.EndsWith("spotify.com", StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Fires the LoginStatusChanged event
        /// </summary>
        /// <param name="status">New login status</param>
        protected virtual void OnLoginStatusChanged(SpotifyLoginStatus status)
        {
            LoginStatusChanged?.Invoke(this, new SpotifyLoginStatusChangedEventArgs(status));
        }
    }

    /// <summary>
    /// Event arguments for Spotify login status changes
    /// </summary>
    public class SpotifyLoginStatusChangedEventArgs : EventArgs
    {
        public SpotifyLoginStatus Status { get; }

        public SpotifyLoginStatusChangedEventArgs(SpotifyLoginStatus status)
        {
            Status = status;
        }
    }
}
