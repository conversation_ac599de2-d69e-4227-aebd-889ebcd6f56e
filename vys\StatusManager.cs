using System;
using System.IO;
using System.Windows;
using System.Windows.Media.Imaging;
using WpfApplication = System.Windows.Application;
using vys.Logging;

namespace vys
{
    /// <summary>
    /// Centralized status management system that coordinates application and system tray status
    /// </summary>
    public class StatusManager : IDisposable
    {
        private readonly SystemTrayManager _systemTrayManager;
        private SpotifyLoginStatus _currentStatus = SpotifyLoginStatus.Unknown;
        private bool _disposed = false;

        public StatusManager()
        {
            _systemTrayManager = new SystemTrayManager();
        }

        /// <summary>
        /// Event fired when the overall application status changes
        /// </summary>
        public event EventHandler<ApplicationStatusChangedEventArgs>? StatusChanged;

        /// <summary>
        /// Gets the current Spotify login status
        /// </summary>
        public SpotifyLoginStatus CurrentStatus => _currentStatus;

        /// <summary>
        /// Updates the application status based on Spotify login state
        /// </summary>
        /// <param name="spotifyStatus">New Spotify login status</param>
        public void UpdateSpotifyStatus(SpotifyLoginStatus spotifyStatus)
        {
            try
            {
                var previousStatus = _currentStatus;
                _currentStatus = spotifyStatus;

                Logger.Info("StatusManager", $"Status updated: {previousStatus} -> {spotifyStatus}");

                // Check for session loss (logged in -> not logged in)
                if (spotifyStatus == SpotifyLoginStatus.NotLoggedIn && previousStatus == SpotifyLoginStatus.LoggedIn)
                {
                    var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                    Logger.Warning("StatusManager", $"[{timestamp}] Spotify session lost - user authentication required for Spotify features");
                }

                // Update system tray icon
                _systemTrayManager.SetStatusIcon(spotifyStatus);

                // Update main window icon if available
                UpdateMainWindowIcon(spotifyStatus);

                // Log status change
                LogStatusChange(spotifyStatus);

                // Fire status changed event
                OnStatusChanged(new ApplicationStatusChangedEventArgs(previousStatus, spotifyStatus));
            }
            catch (Exception ex)
            {
                Logger.Error("StatusManager", ex, "Error updating Spotify status");
                HandleError("Failed to update status", ex);
            }
        }

        /// <summary>
        /// Handles general application errors and updates status accordingly
        /// </summary>
        /// <param name="errorMessage">Error message</param>
        /// <param name="exception">Optional exception details</param>
        public void HandleError(string errorMessage, Exception? exception = null)
        {
            try
            {
                if (exception != null)
                {
                    Logger.Error("StatusManager", exception, errorMessage);
                }
                else
                {
                    Logger.Error("StatusManager", errorMessage);
                }

                // Set error status
                var previousStatus = _currentStatus;
                _currentStatus = SpotifyLoginStatus.Error;

                // Update system tray to show error state
                _systemTrayManager.SetStatusIcon(SpotifyLoginStatus.Error);

                // Update main window icon to show error state
                UpdateMainWindowIcon(SpotifyLoginStatus.Error);

                // Fire status changed event
                OnStatusChanged(new ApplicationStatusChangedEventArgs(previousStatus, SpotifyLoginStatus.Error));
            }
            catch (Exception ex)
            {
                Logger.Error("StatusManager", ex, "Critical error in error handler");
            }
        }

        /// <summary>
        /// Updates the main window icon based on status
        /// </summary>
        /// <param name="status">Current status</param>
        private void UpdateMainWindowIcon(SpotifyLoginStatus status)
        {
            try
            {
                var mainWindow = WpfApplication.Current.MainWindow;
                if (mainWindow == null) return;

                string iconPath;
                switch (status)
                {
                    case SpotifyLoginStatus.LoggedIn:
                        iconPath = "Resources/app_icon_green.ico";
                        break;
                    case SpotifyLoginStatus.NotLoggedIn:
                    case SpotifyLoginStatus.Error:
                        iconPath = "Resources/app_icon_red.ico";
                        break;
                    default:
                        iconPath = "Resources/app_icon.ico";
                        break;
                }

                // Update window icon if file exists
                if (File.Exists(iconPath))
                {
                    try
                    {
                        var iconUri = new Uri($"pack://application:,,,/{iconPath}");
                        mainWindow.Icon = new BitmapImage(iconUri);
                        Logger.Debug("StatusManager", $"Main window icon updated to: {iconPath}");
                    }
                    catch (Exception ex)
                    {
                        Logger.Warning("StatusManager", $"Could not update main window icon: {ex.Message}");
                        // Continue without updating the main window icon
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error("StatusManager", ex, "Error updating main window icon");
            }
        }

        /// <summary>
        /// Logs status changes to console
        /// </summary>
        /// <param name="status">New status</param>
        private void LogStatusChange(SpotifyLoginStatus status)
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            var statusMessage = status switch
            {
                SpotifyLoginStatus.LoggedIn => "User is logged into Spotify",
                SpotifyLoginStatus.NotLoggedIn => "User is not logged into Spotify",
                SpotifyLoginStatus.Error => "Application error state detected",
                _ => "Status unknown"
            };

            Logger.Info("StatusManager", $"[{timestamp}] STATUS: {statusMessage}");
        }

        /// <summary>
        /// Checks if Spotify functionality is available and logs appropriate error if not
        /// </summary>
        /// <param name="operation">The operation being attempted</param>
        /// <returns>True if Spotify is available, false otherwise</returns>
        public bool EnsureSpotifyAvailable(string operation)
        {
            if (_currentStatus == SpotifyLoginStatus.LoggedIn)
            {
                return true;
            }

            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            Logger.Error("StatusManager", $"[{timestamp}] ERROR: Spotify login required for '{operation}' but user is not authenticated");
            return false;
        }

        /// <summary>
        /// Gets a user-friendly status message
        /// </summary>
        /// <returns>Status message for display</returns>
        public string GetStatusMessage()
        {
            return _currentStatus switch
            {
                SpotifyLoginStatus.LoggedIn => "Spotify: Connected",
                SpotifyLoginStatus.NotLoggedIn => "Spotify: Not Connected",
                SpotifyLoginStatus.Error => "Application Error",
                _ => "Checking Status..."
            };
        }

        /// <summary>
        /// Fires the StatusChanged event
        /// </summary>
        /// <param name="args">Event arguments</param>
        protected virtual void OnStatusChanged(ApplicationStatusChangedEventArgs args)
        {
            StatusChanged?.Invoke(this, args);
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _systemTrayManager?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Event arguments for application status changes
    /// </summary>
    public class ApplicationStatusChangedEventArgs : EventArgs
    {
        public SpotifyLoginStatus PreviousStatus { get; }
        public SpotifyLoginStatus NewStatus { get; }

        public ApplicationStatusChangedEventArgs(SpotifyLoginStatus previousStatus, SpotifyLoginStatus newStatus)
        {
            PreviousStatus = previousStatus;
            NewStatus = newStatus;
        }
    }
}
