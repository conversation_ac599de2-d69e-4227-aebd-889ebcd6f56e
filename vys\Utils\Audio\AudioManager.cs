using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using vys.Logging;

namespace vys.Utils.Audio
{
    /// <summary>
    /// Manages application audio state including mute/unmute functionality and persistence
    /// </summary>
    public class AudioManager : IDisposable
    {
        private readonly WindowsAudioApi _audioApi;
        private readonly string _settingsPath;
        private bool _disposed = false;
        private AudioSettings _settings;

        public event EventHandler<MuteStateChangedEventArgs>? MuteStateChanged;

        public AudioManager()
        {
            _audioApi = new WindowsAudioApi();
            
            // Store settings in user's AppData folder
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var appFolder = Path.Combine(appDataPath, "VYS Browser");
            Directory.CreateDirectory(appFolder);
            _settingsPath = Path.Combine(appFolder, "audio-settings.json");
            
            _settings = LoadSettings();

            // Force default to muted as requested
            if (!_settings.IsMuted)
            {
                Logger.Info("AudioManager", $"Forcing default mute state to True (was: {_settings.IsMuted})");
                _settings.IsMuted = true;
                SaveSettings();
            }

            // Apply mute state on startup immediately
            Logger.Debug("AudioManager", $"Applying initial mute state: {_settings.IsMuted}");
            _audioApi.IsMuted = _settings.IsMuted;

            // Also apply it with a delay for any sessions that might appear later
            if (_settings.IsMuted)
            {
                Task.Run(() =>
                {
                    // Wait a bit for audio session to be available
                    System.Threading.Thread.Sleep(1000);
                    Logger.Debug("AudioManager", $"Re-applying mute state after delay: {_settings.IsMuted}");
                    _audioApi.IsMuted = _settings.IsMuted;
                });
            }
        }

        /// <summary>
        /// Gets the current mute state
        /// </summary>
        public bool IsMuted => _audioApi.IsMuted;

        /// <summary>
        /// Gets or sets the volume level (0.0 to 1.0)
        /// </summary>
        public float Volume
        {
            get => _audioApi.Volume;
            set
            {
                _audioApi.Volume = value;
                _settings.Volume = value;
                SaveSettings();
            }
        }

        /// <summary>
        /// Toggles the mute state
        /// </summary>
        public void ToggleMute()
        {
            var newMuteState = !IsMuted;
            SetMute(newMuteState);
        }

        /// <summary>
        /// Sets the mute state
        /// </summary>
        /// <param name="muted">True to mute, false to unmute</param>
        public void SetMute(bool muted)
        {
            var oldState = IsMuted;

            Logger.Debug("AudioManager", $"Setting mute state to {muted}");

            // Set the mute state - this will work immediately even without active audio
            _audioApi.IsMuted = muted;
            _settings.IsMuted = muted;
            SaveSettings();

            // Always refresh to ensure any existing sessions get the new state
            _audioApi.RefreshAudioSession();

            var newState = IsMuted;
            Logger.Info("AudioManager", $"Mute state changed from {oldState} to {newState}");

            // Always fire the event to update UI
            OnMuteStateChanged(new MuteStateChangedEventArgs(muted));
        }

        /// <summary>
        /// Refreshes the audio session connection
        /// Call this when audio starts playing or if the session is lost
        /// </summary>
        public void RefreshAudioSession()
        {
            _audioApi.RefreshAudioSession();
            
            // Reapply mute state after refresh
            if (_settings.IsMuted)
            {
                _audioApi.IsMuted = true;
            }
        }

        private AudioSettings LoadSettings()
        {
            try
            {
                if (File.Exists(_settingsPath))
                {
                    var json = File.ReadAllText(_settingsPath);
                    var settings = JsonSerializer.Deserialize<AudioSettings>(json);
                    if (settings != null)
                    {
                        Logger.Debug("AudioManager", $"Loaded audio settings: IsMuted={settings.IsMuted}, Volume={settings.Volume}");
                        return settings;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Warning("AudioManager", $"Failed to load audio settings: {ex.Message}");
            }

            var defaultSettings = new AudioSettings();
            Logger.Debug("AudioManager", $"Using default audio settings: IsMuted={defaultSettings.IsMuted}, Volume={defaultSettings.Volume}");

            // Save the default settings immediately so they persist
            try
            {
                var json = JsonSerializer.Serialize(defaultSettings, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(_settingsPath, json);
                Logger.Debug("AudioManager", $"Saved default settings to {_settingsPath}");
            }
            catch (Exception ex)
            {
                Logger.Warning("AudioManager", $"Failed to save default settings: {ex.Message}");
            }

            return defaultSettings;
        }

        private void SaveSettings()
        {
            try
            {
                var json = JsonSerializer.Serialize(_settings, new JsonSerializerOptions 
                { 
                    WriteIndented = true 
                });
                File.WriteAllText(_settingsPath, json);
            }
            catch (Exception ex)
            {
                Logger.Warning("AudioManager", $"Failed to save audio settings: {ex.Message}");
            }
        }

        protected virtual void OnMuteStateChanged(MuteStateChangedEventArgs e)
        {
            MuteStateChanged?.Invoke(this, e);
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _audioApi?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Audio settings for persistence
    /// </summary>
    public class AudioSettings
    {
        public bool IsMuted { get; set; } = true;  // Default to muted
        public float Volume { get; set; } = 1.0f;
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Event arguments for mute state changes
    /// </summary>
    public class MuteStateChangedEventArgs : EventArgs
    {
        public bool IsMuted { get; }

        public MuteStateChangedEventArgs(bool isMuted)
        {
            IsMuted = isMuted;
        }
    }
}
