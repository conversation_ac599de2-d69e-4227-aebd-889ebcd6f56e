2025/07/21-16:13:54.999 6ba4 Reusing MANIFEST C:\Users\<USER>\Desktop\ysviewer\vys\bin\Debug\net9.0-windows\win-x64\vys.exe.WebView2\EBWebView\Default\IndexedDB\https_open.spotify.com_0.indexeddb.leveldb/MANIFEST-000001
2025/07/21-16:13:55.000 6ba4 Recovering log #541
2025/07/21-16:13:55.001 6ba4 Reusing old log C:\Users\<USER>\Desktop\ysviewer\vys\bin\Debug\net9.0-windows\win-x64\vys.exe.WebView2\EBWebView\Default\IndexedDB\https_open.spotify.com_0.indexeddb.leveldb/000541.log 
2025/07/21-16:13:55.001 6ba4 Delete type=2 #540
2025/07/21-16:13:55.002 6ba4 Delete type=2 #542
2025/07/21-16:13:55.473 4b0c Level-0 table #546: started
2025/07/21-16:13:55.491 4b0c Level-0 table #546: 4165985 bytes OK
2025/07/21-16:13:55.493 4b0c Delete type=0 #541
2025/07/21-16:13:55.580 6ba4 Compacting 1@0 + 1@1 files
2025/07/21-16:13:55.594 6ba4 Generated table #547@0: 8840 keys, 590985 bytes
2025/07/21-16:13:55.594 6ba4 Compacted 1@0 + 1@1 files => 590985 bytes
2025/07/21-16:13:55.595 6ba4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/07/21-16:13:56.999 648 Level-0 table #549: started
2025/07/21-16:13:57.016 648 Level-0 table #549: 4263796 bytes OK
2025/07/21-16:13:57.017 648 Delete type=2 #543
2025/07/21-16:13:57.017 648 Delete type=0 #545
2025/07/21-16:13:57.017 648 Delete type=2 #546
2025/07/21-16:13:58.498 6ba4 Compacting 1@0 + 1@1 files
2025/07/21-16:13:58.510 6ba4 Generated table #550@0: 8847 keys, 538521 bytes
2025/07/21-16:13:58.510 6ba4 Compacted 1@0 + 1@1 files => 538521 bytes
2025/07/21-16:13:58.511 6ba4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/07/21-16:14:08.849 4b0c Level-0 table #552: started
2025/07/21-16:14:08.865 4b0c Level-0 table #552: 4081883 bytes OK
2025/07/21-16:14:08.866 4b0c Delete type=2 #547
2025/07/21-16:14:08.866 4b0c Delete type=0 #548
2025/07/21-16:14:08.866 4b0c Delete type=2 #549
2025/07/21-16:14:19.781 6ba4 Compacting 1@0 + 1@1 files
2025/07/21-16:14:19.793 6ba4 Generated table #553@0: 8898 keys, 543372 bytes
2025/07/21-16:14:19.793 6ba4 Compacted 1@0 + 1@1 files => 543372 bytes
2025/07/21-16:14:19.797 6ba4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/07/21-16:14:37.965 4b0c Level-0 table #555: started
2025/07/21-16:14:37.981 4b0c Level-0 table #555: 4181250 bytes OK
2025/07/21-16:14:37.982 4b0c Delete type=2 #550
2025/07/21-16:14:37.982 4b0c Delete type=0 #551
2025/07/21-16:14:37.982 4b0c Delete type=2 #552
2025/07/21-16:14:38.077 6ba4 Compacting 1@0 + 1@1 files
2025/07/21-16:14:38.088 6ba4 Generated table #556@0: 8949 keys, 600527 bytes
2025/07/21-16:14:38.088 6ba4 Compacted 1@0 + 1@1 files => 600527 bytes
2025/07/21-16:14:38.089 6ba4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/07/21-16:15:52.536 648 Level-0 table #558: started
2025/07/21-16:15:52.552 648 Level-0 table #558: 4257847 bytes OK
2025/07/21-16:15:52.553 648 Delete type=2 #553
2025/07/21-16:15:52.553 648 Delete type=0 #554
2025/07/21-16:15:52.553 648 Delete type=2 #555
2025/07/21-16:15:53.310 6ba4 Compacting 1@0 + 1@1 files
2025/07/21-16:15:53.322 6ba4 Generated table #559@0: 8959 keys, 549338 bytes
2025/07/21-16:15:53.322 6ba4 Compacted 1@0 + 1@1 files => 549338 bytes
2025/07/21-16:15:53.323 6ba4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/07/21-16:15:53.562 648 Level-0 table #561: started
2025/07/21-16:15:53.586 648 Level-0 table #561: 4252690 bytes OK
2025/07/21-16:15:53.587 648 Delete type=2 #556
2025/07/21-16:15:53.587 648 Delete type=0 #557
2025/07/21-16:15:53.588 648 Delete type=2 #558
2025/07/21-16:15:53.609 6ba4 Compacting 1@0 + 1@1 files
2025/07/21-16:15:53.624 6ba4 Generated table #562@0: 8960 keys, 549371 bytes
2025/07/21-16:15:53.624 6ba4 Compacted 1@0 + 1@1 files => 549371 bytes
2025/07/21-16:15:53.625 6ba4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/07/21-16:15:54.191 4b0c Level-0 table #564: started
2025/07/21-16:15:54.210 4b0c Level-0 table #564: 4265223 bytes OK
2025/07/21-16:15:54.211 4b0c Delete type=2 #559
2025/07/21-16:15:54.211 4b0c Delete type=0 #560
2025/07/21-16:15:54.211 4b0c Delete type=2 #561
2025/07/21-16:15:56.772 6ba4 Compacting 1@0 + 1@1 files
2025/07/21-16:15:56.786 6ba4 Generated table #565@0: 8974 keys, 604047 bytes
2025/07/21-16:15:56.787 6ba4 Compacted 1@0 + 1@1 files => 604047 bytes
2025/07/21-16:15:56.791 6ba4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/07/21-16:16:33.124 6ba4 Level-0 table #567: started
2025/07/21-16:16:33.142 6ba4 Level-0 table #567: 3999891 bytes OK
2025/07/21-16:16:33.144 6ba4 Delete type=2 #562
2025/07/21-16:16:33.144 6ba4 Delete type=0 #563
2025/07/21-16:16:33.144 6ba4 Delete type=2 #564
2025/07/21-16:16:33.509 6ba4 Compacting 1@0 + 1@1 files
2025/07/21-16:16:33.520 6ba4 Generated table #568@0: 9215 keys, 458399 bytes
2025/07/21-16:16:33.520 6ba4 Compacted 1@0 + 1@1 files => 458399 bytes
2025/07/21-16:16:33.521 6ba4 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/07/21-16:16:48.107 6ba4 Level-0 table #570: started
2025/07/21-16:16:48.128 6ba4 Level-0 table #570: 4079565 bytes OK
2025/07/21-16:16:48.130 6ba4 Delete type=2 #565
2025/07/21-16:16:48.130 6ba4 Delete type=0 #566
2025/07/21-16:16:48.130 6ba4 Delete type=2 #567
2025/07/21-16:16:52.105 6ba4 Compacting 1@0 + 1@1 files
2025/07/21-16:16:52.117 6ba4 Generated table #571@0: 9392 keys, 520200 bytes
2025/07/21-16:16:52.117 6ba4 Compacted 1@0 + 1@1 files => 520200 bytes
2025/07/21-16:16:52.118 6ba4 compacted to: files[ 0 1 1 0 0 0 0 ]
