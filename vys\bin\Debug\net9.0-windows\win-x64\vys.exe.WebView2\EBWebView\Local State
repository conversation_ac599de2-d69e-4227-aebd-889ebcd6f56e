{"accessibility": {"captions": {"common_models_path": "", "soda_binary_path": ""}}, "autofill": {"ablation_seed": "Sst2rWbywLw="}, "breadcrumbs": {"enabled": true, "enabled_time": "13397357421695265"}, "default_browser": {"browser_name_enum": 2}, "desktop_session_duration_tracker": {"last_session_end_timestamp": "**********"}, "domain_actions_config": "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", "edge": {"manageability": {"edge_last_active_time": "13397584647775299"}, "mitigation_manager": {"renderer_app_container_compatible_count": 48, "renderer_code_integrity_compatible_count": 48}, "tab_stabs": {"closed_without_unfreeze_never_unfrozen": 0, "closed_without_unfreeze_previously_unfrozen": 0, "discard_without_unfreeze_never_unfrozen": 0, "discard_without_unfreeze_previously_unfrozen": 0}, "tab_stats": {"frozen_daily": 0, "unfrozen_daily": 0}}, "edge_ci": {"num_healthy_browsers_since_failure": 14}, "hardware_acceleration_mode_previous": true, "identity_combined_status": {"aad": 1, "ad": 1}, "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "network_time": {"network_time_mapping": {"local": 1753110746730.151, "network": 1753110747000.0, "ticks": 152966142542.0, "uncertainty": 1289488.0}}, "optimization_guide": {"model_execution": {"last_usage_by_feature": {}}, "model_store_metadata": {}, "on_device": {"last_version": "138.0.3351.95", "model_crash_count": 0}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAACRp0AeAsjPT6P1CH+duOVREAAAAB4AAABNAGkAYwByAG8AcwBvAGYAdAAgAEUAZABnAGUAAAAQZgAAAAEAACAAAABfC9rEYEm+xjDkPMll1L+gP5RZi4e2Mulh1c+sPmTbHAAAAAAOgAAAAAIAACAAAACSXP1YHTNDe7vwWmOgc1TvjJuv/fzLB+yZefMzN89P/jAAAAAxtOdSsCRIbJEJYBDXd2CnaZa25NvP9ImY8WtUpfQbBpzrguhKs7ZWdLRFR82bvPtAAAAAZgU3fXUIo3i9MoNd8s0JlWrzCuWVNYHGcL+CGsaCymbmsV0+zj9vxb8IDVclIpyxkVRuXPrc6M4jbV2H8zkFNg=="}, "performance_intervention": {"last_daily_sample": "*****************"}, "policy": {"last_statistics_update": "*****************"}, "profile": {"info_cache": {"Default": {"active_time": **********.77482, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_20", "background_apps": false, "edge_account_cid": "", "edge_account_environment": 0, "edge_account_environment_string": "", "edge_account_first_name": "", "edge_account_last_name": "", "edge_account_oid": "", "edge_account_sovereignty": 0, "edge_account_tenant_id": "", "edge_account_type": 0, "edge_profile_can_be_deleted": true, "edge_test_on_premises": false, "edge_wam_aad_for_app_account_type": 0, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_given_name": "", "gaia_id": "", "gaia_name": "", "hosted_domain": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_glic_eligible": false, "is_using_default_avatar": true, "is_using_default_name": true, "managed_user_id": "", "metrics_bucket_index": 1, "name": "Profile 1", "signin.with_credential_provider": false, "user_name": ""}}, "last_active_profiles": [], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "profiles": {"edge": {"guided_switch_pref": [], "multiple_profiles_with_same_account": false}, "edge_sso_info": {"msa_first_profile_key": "<PERSON><PERSON><PERSON>", "msa_sso_algo_state": 1}, "signin_last_seen_version": "138.0.3351.95", "signin_last_updated_time": **********.39873}, "sentinel_creation_time": "0", "session_id_generator_last_value": "**********", "signin": {"active_accounts_last_emitted": "*****************"}, "startup_boost": {"last_browser_open_time": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": **********, "content": "**********", "format": 36}}, "tab_stats": {"discards_external": 0, "discards_proactive": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 1, "reloads_external": 0, "reloads_urgent": 0, "total_tab_count_max": 1, "window_count_max": 1}, "telemetry_client": {"cloned_install": {"user_data_dir_id": ********}, "governance": {"last_dma_change_date": "*****************", "last_known_cps": 0}, "host_telclient_path": "QzpcUHJvZ3JhbSBGaWxlcyAoeDg2KVxNaWNyb3NvZnRcRWRnZVdlYlZpZXdcQXBwbGljYXRpb25cMTM4LjAuMzM1MS45NVx0ZWxjbGllbnQuZGxs", "sample_id": ********}, "uninstall_metrics": {"installation_date2": "**********"}, "updateclientdata": {"apps": {"ahmaebgpfccdhgidjaidaoojjcijckba": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "alpjnmnfbgfkmmpcfpejmmoebdndedno": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "********"}, "eeobbhfgfagbclfofmgbdfoicabjdbkn": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "fgbafbciocncjfbbonhocjaohoknlaco": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "2025.5.15.1"}, "fppmbhmldokgmleojlplaaodlkibgikh": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "********"}, "hajigopbbjhghbfimgkfmpenfkclmohk": {"cohort": "", "cohortname": "", "installdate": -1}, "jbfaflocpnkhbgcijpkiafdpbjkedane": {"cohort": "", "cohortname": "", "installdate": -1}, "kpfehajjjbbcifeehjgfgnabifknmdad": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "120.0.6050.0"}, "ldfkbgjbencjpgjfleiooeldhjdapggh": {"cohort": "", "cohortname": "", "installdate": -1}, "mcfjlbnicoclaecapilmleaelokfnijm": {"cohort": "", "cohortname": "", "installdate": -1}, "ndikpojcjlepofdkaaldkinkjbeeebkl": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "**********"}, "oankkpibpaokgecfckkdkgaoafllipag": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "6498.2024.12.2"}, "ohckeflnhegojcjlcpbfpciadgikcohk": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "*******"}, "ojblfafjmiikbkepnnolpgbbhejhlcim": {"cohort": "", "cohortname": "", "installdate": -1}, "pghocgajpebopihickglahgebcmkcekh": {"cohort": "", "cohortname": "", "fp": "", "installdate": -1, "max_pv": "0.0.0.0", "pv": "********"}, "pmagihnlncbcefglppponlgakiphldeh": {"cohort": "", "cohortname": "", "installdate": -1}}}, "updateclientlastupdatecheckerror": 0, "updateclientlastupdatecheckerrorcategory": 0, "updateclientlastupdatecheckerrorextracode1": 0, "user_experience_metrics": {"client_id2": "{F3017226-FE2A-4295-8BDF-00C3A9A7E4C5}C:\\Users\\<USER>", "diagnostics": {"last_data_collection_level_on_launch": 1}, "limited_entropy_randomization_source": "4FC4FF1C167017D0456B3F36300BAFFC", "low_entropy_source3": 2359, "machine_id": 7947189, "payload_counter": 1, "pseudo_low_entropy_source": 5651, "reporting_enabled": false, "reset_client_id_deterministic": true, "session_id": 47, "stability": {"browser_last_live_timestamp": "13397584647869424", "exited_cleanly": true, "stats_buildtime": "1752620643", "stats_version": "138.0.3351.95-64", "system_crash_count": 0}}, "variations_compressed_seed": "safe_seed_content", "variations_config_ids": "{\"ECS\":\"P-R-1082570-1-11,P-D-42388-2-6\",\"EdgeConfig\":\"P-R-1627497-3-8,P-R-1612140-3-4,P-R-1541171-6-9,P-R-1528200-3-4,P-R-1480299-3-5,P-R-1459857-3-2,P-R-1459074-3-9,P-R-1447912-3-12,P-R-1315481-1-6,P-R-1160552-1-3,P-R-1133477-3-4,P-R-1113531-4-9,P-R-1082109-3-6,P-R-1054140-1-4,P-R-1049918-1-3,P-R-68474-9-12,P-R-60617-8-21,P-R-45373-8-85\",\"EdgeFirstRunConfig\":\"P-R-1253659-3-4,P-R-1075865-4-7\",\"Segmentation\":\"P-R-1473016-1-8,P-R-1159985-1-5,P-R-1113915-25-11,P-R-1098334-1-6,P-R-66078-1-3,P-R-66077-1-5,P-R-60882-1-2,P-R-43082-3-5,P-R-42744-1-2\"}", "variations_country": "PT", "variations_crash_streak": 0, "variations_failed_to_fetch_seed_streak": 1, "variations_google_groups": {"Default": []}, "variations_last_fetch_time": "13397584345638185", "variations_last_runtime_fetch_time": "13397584353412854", "variations_limited_entropy_synthetic_trial_seed_v2": "94", "variations_permanent_consistency_country": ["138.0.3351.95", "PT"], "variations_runtime_compressed_seed": "H4sIAAAAAAAAAG2QW2vbQBCF/8s8a+juzF4FfQiWi90LGDshhaoPqrV1BLZUbLnBNvrvZbWOE2gez8w5u/OdC0y69nezmRcHyOFSwnSyKiEvYYFLlMKRtgIlSpktsEBF7BwSmhKyEqb1JhTdrmrau3XfdO3hTdAzKR2DPksDtp5NHKjrgJTx/PL0+JcRCiWyGbV1LEa/S5INjdImKQVp1Ch5lIal0aiQUlYzKUbC5FVCeEZ+WbKzSqFFn7YsnfdIHpXXI6OU2hmpkFBdNTuWLl52o14e277ZhVTdG2p21ggk9KNzFTa70PZV7ObVpCwLGVESmJTaexer0lct2UuNpF+b8Y45VpMAjBF2POem7C1thHOEEimRs3CEfN0piuASqYQBsv85IL/AdPenP727CW31axs+hao/7sMB8h8Qwn4NP4chg1mo6rA/RNukO7b9/jTp6gA5LO7jR/fVBnIoYfaFi79OPX9dmofz9mm1/XyqyU3nzezx+/nb3D19qM+P2+J53aiPJcAw/AOe7zUYnQIAAA==", "variations_safe_compressed_seed": "H4sIAAAAAAAAAJVWW3PaOBT+Kxk9W1nLd7OzD4RLSwuzbIDS6baTEfbBaGJLriSHMJn89x3ZhkAad9s8ZNDR933nqgNPaDRYoN4TGj0meZXC6FGD5DQfCL5l2SRVEz4VGeppWYGFGutUZEsqM9CohyDN4E5puskBPVtolGbQgGrNNIMxy2FxUBqKfpKAUsaeMmUIY6C6kqBQ71/0GrY4FBuRs2TK+P1gB8k9+vZsoY8szxd7ppMdXimawegBuB5STTtUCzXhdVqX6AkfA6Qb2qoOW+KrEGq3OVP6F0MeMgmJFvIw0SCpZoLf5KJReMlgxI3M+pPTT1NmMDSfS2HoJjdIl5BDAVoejFPgPya0hs0nBvv/559ltv7kLBk/rBlPxf4WaM70oQmpq2wdcKvzqs/TVZlSDU2WxWO/ZMudFFrnYCJTddc6/V0SXsU+oAVIWrdwJjjTQjKedUjNIGV0LCqe1h14m1qP0uKGJveZNNBbUKKSCYyhO8YutNHaPzgLytONeOwgT5TIqYYWBOlkK2kBqiavhczTQVWOc0H14TiL9WEmUugs2RnEyEwFTReaaqY0S9RUZBnj2VyyTv5rwn4uWUurBRdpsmumtWsUzfM2qCYN2PQrvZtTpe7hoFaTeh7l4md9bznANUvqdr2wmzEyDnaiLJtUEhiIoqQSbkGVgquzTfNWeEfmipZs9FiSn0r20ynl2d8PICVLf0/YrYX/WU0GhlBS01oN0oCfEKcFoB5KdpRzyJGFHmheGcsYPVunayhFsju7dO3m7xwjQUvKVcF0vW3vBL/bMwl3mhUgKn1XsDxnChLBU3Um5RgVE+Axt3oLqs6e1rf9PBf7qVl936yOjFroUbUGv3idsUQKJbb6eg2bGyn2CuT1XAotNtX2evVxdt1szrkUW5aDZVvE//MXSJpqdT0V2UJTqS3b8n6HZFqdg4aa1xTltC7nolxQc13/b/fLWwUaC5nAiXZCW/WLypag9IqzrZDFkCkt2aYyc/2eKS0ySYvueprdUjqnMC46eD4GKWxplet3Bv4W+gJc3Redkt+e2+cwZlLp24q/DP0QTJnGt6O2PfU4QDqnh1zQtHOjdNDab4RSH9729mOVAbYyQXWAC8gK4LreDgY860/6eY56aEtzZVJar0eXBpWU28pY7PpzVlGZHg8PIOuPMzfwB0LCJXMguAauFyAfWALqlR/YfF5emm4Yzy4tJsFLy2k8V8r4bu3PFnoPNK1H4AmNljRDPfQVTYWtvqzcL3787nM/mg6D+ahMhmL3fR1uh38cYDxmG/ohePweeH99RcbfY8nqmqEF1dYVia8+VPmVYzv+lU16xO75/tW72bLOreJaHgb1NwqaG5N5F5VqLfWaOP7AmwybuMwvQzTHt5jYkeOHNiaYEGuOh9hz3CjCDg7Q5W++Bh04oReH2MWR1ZyJQzwbu9hrzr5HSEhwgOP27ESOfXbvRbYTx9jFfnv248g3es7pbIcedo98zwtj4mAXkxbgEt+LCCY4aM4ksH3fwQS77dl1vTB8cUiI67sEe0dBO3KIbQJo+bbvmQTIEW97cUyik14QeaGH46P7wA5IiCPskPro+W7o4ghHPnr7wTVFc3w38OOXmOzQjwIfezhEr59BQ/BC1yYBJscqEz+OIx+TY9UIcWPiY8dvmmYk48h1vVNZgsAOz5II7DA8sQM7ikzBmow8146cUz88J/SMioOen/8DyUUz9kAMAAA=", "variations_safe_seed_date": "13397529774000000", "variations_safe_seed_fetch_time": "13397529775450066", "variations_safe_seed_locale": "en-US", "variations_safe_seed_milestone": 138, "variations_safe_seed_permanent_consistency_country": "PT", "variations_safe_seed_session_consistency_country": "PT", "variations_safe_seed_signature": "", "variations_seed_client_version_at_store": "138.0.3351.95", "variations_seed_date": "13397584346000000", "variations_seed_etag": "\"Lo0sZU3Z59GXA8LD6PEpcDohqW7fD/yeFFibaJ6xq64=\"", "variations_seed_milestone": 138, "variations_seed_runtime_etag": "\"HK3Dv84wLR6UzlhSlJyd28EIiHWXzMI8h/dzWlDwci4=\"", "variations_seed_signature": "", "was": {"restarted": false}}