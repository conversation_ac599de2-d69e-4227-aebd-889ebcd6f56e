﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>

    <Platforms>AnyCPU;x64;x86</Platforms>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <SelfContained>false</SelfContained>
    <ApplicationIcon>Resources\app_icon.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.2792.45" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
    <PackageReference Include="Hardcodet.NotifyIcon.Wpf" Version="1.1.0" />
    <PackageReference Include="NAudio" Version="2.2.1" />
    <PackageReference Include="System.Management" Version="9.0.0" />
  </ItemGroup>



  <ItemGroup>
    <Resource Include="Resources\app_icon.ico" />
    <Resource Include="Resources\app_icon.png" />
    <Resource Include="Resources\app_icon_green.ico" />
    <Resource Include="Resources\app_icon_green.png" />
    <Resource Include="Resources\app_icon_red.ico" />
    <Resource Include="Resources\app_icon_red.png" />
  </ItemGroup>

</Project>
